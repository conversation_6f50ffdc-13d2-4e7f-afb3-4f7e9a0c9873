# Runtime Issues Analysis

## Current Problems

### 1. Node.js Path Issues

**Error:** `exec: node: not found` when running `pnpm dev` commands

**Root Cause:**

- Node.js not in PATH for some terminal sessions
- NVM-managed Node.js not properly linked

**Solutions:**

1. **Immediate Fix:** Use full path to node in scripts
2. **Long-term:** Ensure NVM integration in development setup

### 2. ESLint Runtime Conflicts

**Error:** `Cannot read properties of undefined (reading 'allowShortCircuit')`

**Root Cause:**

- Version mismatch between ESLint core and TypeScript ESLint plugin
- Rule configuration incompatibility

**Status:** ✅ **Fixed for webchat.test-client**
**Remaining:** ai.test-server and other NestJS projects

## Recommended Actions

### 1. Fix Node.js PATH Issues

```bash
# Add to package.json scripts
"dev": "node --version && pnpm --filter @lisa/project dev"
```

### 2. Standardize Development Environment

- Document NVM setup requirements
- Add health checks before dev commands
- Consider using Docker for consistent environments

### 3. ESLint Version Alignment

- Upgrade all projects to ESLint 9.x
- Use compatible TypeScript ESLint versions
- Test all lint configurations

## Current Status Summary

| Component     | Status     | Notes                      |
| ------------- | ---------- | -------------------------- |
| Build         | ✅ Working | All projects compile       |
| Docker Build  | ✅ Working | ai.api builds successfully |
| Lint (React)  | ✅ Working | webchat.test-client fixed  |
| Lint (NestJS) | ❌ Failing | Version conflicts          |
| Dev Server    | ❌ Failing | Node.js PATH issues        |

## Next Steps Priority

1. Fix ESLint version conflicts (HIGH)
2. Resolve Node.js PATH issues (MEDIUM)
3. Consolidate configurations (LOW)
4. Add comprehensive testing (LOW)
