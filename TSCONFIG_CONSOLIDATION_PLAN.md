# TSConfig Consolidation Plan

## Current Issues

- Multiple duplicate configurations
- Inconsistent settings between projects
- Some projects not extending base configs

## Recommended Structure

### 1. Keep Root Configs (Base Templates)

- `tsconfig.json` - Main workspace config with paths
- `tsconfig.base.lib.json` - For libraries (ESM)
- `tsconfig.base.nestjs.json` - For NestJS apps (CommonJS)
- `tsconfig.base.react.json` - For React apps (NEW)

### 2. Consolidate Library Configs

**Remove duplicates:**

- `libs/common/tsconfig.build.json` → Use `tsconfig.lib.json` only
- `libs/crm-sdk/tsconfig.build.json` → Use `tsconfig.lib.json` only

**Standardize library configs to:**

```json
{
  "extends": "../../tsconfig.base.lib.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]
}
```

### 3. Standardize App Configs

**NestJS Apps should extend base:**

```json
{
  "extends": "../../tsconfig.base.nestjs.json",
  "compilerOptions": {
    "declaration": false,
    "outDir": "./dist"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]
}
```

**React Apps should extend new base:**

```json
{
  "extends": "../../tsconfig.base.react.json",
  "compilerOptions": {
    "outDir": "./dist"
  },
  "include": ["src/**/*", "vite.config.ts"],
  "exclude": ["node_modules", "dist"]
}
```

### 4. Remove Redundant Files

- `apps/ai.test-server/tsconfig.json` (use only tsconfig.app.json)
- `apps/webchat.test-client/tsconfig.node.json` (consolidate into main)
- All `tsconfig.build.json` in libs (use tsconfig.lib.json)

## Benefits

- Reduced duplication
- Consistent configurations
- Easier maintenance
- Clear separation by project type
